import { apiClient } from '../config/api.js';
import { API_CONFIG } from '../config/api.js';

export class AssessmentService {
  /**
   * Submit assessment data to the API
   * @param {Object} assessmentData - The assessment data to submit
   * @returns {Promise<Object>} API response
   */
  static async submitAssessment(assessmentData) {
    try {
      const response = await apiClient.post(API_CONFIG.ENDPOINTS.ASSESSMENT.SUBMIT, assessmentData);
      return response;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get assessment status by job ID
   * @param {string} jobId - The job ID to check status for
   * @returns {Promise<Object>} API response with status
   */
  static async getAssessmentStatus(jobId) {
    try {
      const response = await apiClient.get(API_CONFIG.ENDPOINTS.ASSESSMENT.STATUS.replace(':jobId', jobId));
      return response;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get queue status
   * @returns {Promise<Object>} API response with queue status
   */
  static async getQueueStatus() {
    try {
      const response = await apiClient.get(API_CONFIG.ENDPOINTS.ASSESSMENT.QUEUE_STATUS);
      return response;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Convert Likert scale (1-7) to 0-100 scale
   * @param {number} likertValue - Value from 1-7 Likert scale
   * @returns {number} Value on 0-100 scale
   */
  static convertLikertToScore(likertValue) {
    if (likertValue < 1 || likertValue > 7) {
      throw new Error('Likert value must be between 1 and 7');
    }
    return Math.round((likertValue - 1) * (100 / 6));
  }

  /**
   * Apply reverse scoring for BFI items
   * @param {number} likertValue - Original Likert value (1-7)
   * @returns {number} Reverse scored Likert value (1-7)
   */
  static reverseScore(likertValue) {
    if (likertValue < 1 || likertValue > 7) {
      throw new Error('Likert value must be between 1 and 7');
    }
    return 8 - likertValue;
  }

  /**
   * Calculate RIASEC scores from responses
   * @param {Object} responses - User responses for RIASEC questions
   * @returns {Object} RIASEC scores (0-100)
   */
  static calculateRiasecScores(responses) {
    const dimensions = ['realistic', 'investigative', 'artistic', 'social', 'enterprising', 'conventional'];
    const scores = {};

    dimensions.forEach(dimension => {
      const dimensionResponses = responses[dimension] || [];
      if (dimensionResponses.length === 0) {
        scores[dimension] = 0;
        return;
      }

      const sum = dimensionResponses.reduce((acc, response) => {
        return acc + this.convertLikertToScore(response);
      }, 0);
      
      scores[dimension] = Math.round(sum / dimensionResponses.length);
    });

    return scores;
  }

  /**
   * Calculate Big Five (OCEAN) scores from responses
   * @param {Object} responses - User responses for BFI questions
   * @param {Object} questionData - BFI question data with reverse scoring info
   * @returns {Object} OCEAN scores (0-100)
   */
  static calculateOceanScores(responses, questionData) {
    const dimensions = ['openness', 'conscientiousness', 'extraversion', 'agreeableness', 'neuroticism'];
    const scores = {};

    dimensions.forEach(dimension => {
      const dimensionResponses = responses[dimension] || [];
      const dimensionQuestions = questionData[dimension] || [];
      
      if (dimensionResponses.length === 0) {
        scores[dimension] = 0;
        return;
      }

      const sum = dimensionResponses.reduce((acc, response, index) => {
        let adjustedResponse = response;
        
        // Apply reverse scoring if needed
        if (dimensionQuestions[index] && dimensionQuestions[index].reverse) {
          adjustedResponse = this.reverseScore(response);
        }
        
        return acc + this.convertLikertToScore(adjustedResponse);
      }, 0);
      
      scores[dimension] = Math.round(sum / dimensionResponses.length);
    });

    return scores;
  }

  /**
   * Calculate VIA Character Strengths scores from responses
   * @param {Object} responses - User responses for VIA questions
   * @returns {Object} VIA scores (0-100)
   */
  static calculateViaScores(responses) {
    const strengths = [
      'creativity', 'curiosity', 'judgment', 'loveOfLearning', 'perspective',
      'bravery', 'perseverance', 'honesty', 'zest', 'love', 'kindness',
      'socialIntelligence', 'teamwork', 'fairness', 'leadership', 'forgiveness',
      'humility', 'prudence', 'selfRegulation', 'appreciationOfBeauty',
      'gratitude', 'hope', 'humor', 'spirituality'
    ];
    
    const scores = {};

    strengths.forEach(strength => {
      const strengthResponses = responses[strength] || [];
      if (strengthResponses.length === 0) {
        scores[strength] = 0;
        return;
      }

      const sum = strengthResponses.reduce((acc, response) => {
        return acc + this.convertLikertToScore(response);
      }, 0);
      
      scores[strength] = Math.round(sum / strengthResponses.length);
    });

    return scores;
  }

  /**
   * Process all assessment responses and prepare data for API submission
   * @param {Object} allResponses - All user responses
   * @param {Object} questionData - Question data with metadata
   * @returns {Object} Formatted assessment data for API
   */
  static processAssessmentData(allResponses, questionData) {
    const riasecScores = this.calculateRiasecScores(allResponses.riasec || {});
    const oceanScores = this.calculateOceanScores(allResponses.bfi || {}, questionData.bfi);
    const viaScores = this.calculateViaScores(allResponses.via || {});

    return {
      assessmentName: "AI-Driven Talent Mapping",
      riasec: riasecScores,
      ocean: oceanScores,
      viaIs: viaScores
    };
  }

  /**
   * Validate assessment responses
   * @param {Object} responses - User responses to validate
   * @returns {Object} Validation result with isValid and errors
   */
  static validateResponses(responses) {
    const errors = [];
    
    // Check RIASEC responses
    const riasecDimensions = ['realistic', 'investigative', 'artistic', 'social', 'enterprising', 'conventional'];
    riasecDimensions.forEach(dimension => {
      const dimensionResponses = responses.riasec?.[dimension] || [];
      if (dimensionResponses.length !== 10) {
        errors.push(`RIASEC ${dimension}: Expected 10 responses, got ${dimensionResponses.length}`);
      }
    });

    // Check BFI responses
    const bfiDimensions = {
      openness: 10,
      conscientiousness: 9,
      extraversion: 8,
      agreeableness: 9,
      neuroticism: 8
    };
    
    Object.entries(bfiDimensions).forEach(([dimension, expectedCount]) => {
      const dimensionResponses = responses.bfi?.[dimension] || [];
      if (dimensionResponses.length !== expectedCount) {
        errors.push(`BFI ${dimension}: Expected ${expectedCount} responses, got ${dimensionResponses.length}`);
      }
    });

    // Check VIA responses
    const viaStrengths = [
      'creativity', 'curiosity', 'judgment', 'loveOfLearning', 'perspective',
      'bravery', 'perseverance', 'honesty', 'zest', 'love', 'kindness',
      'socialIntelligence', 'teamwork', 'fairness', 'leadership', 'forgiveness',
      'humility', 'prudence', 'selfRegulation', 'appreciationOfBeauty',
      'gratitude', 'hope', 'humor', 'spirituality'
    ];
    
    viaStrengths.forEach(strength => {
      const strengthResponses = responses.via?.[strength] || [];
      if (strengthResponses.length !== 4) {
        errors.push(`VIA ${strength}: Expected 4 responses, got ${strengthResponses.length}`);
      }
    });

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}
