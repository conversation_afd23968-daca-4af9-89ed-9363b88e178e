import { AssessmentSection } from "../components/assessment/AssessmentSection.js";
import { AssessmentService } from "../services/assessmentService.js";
import {
  RIASEC_QUESTIONS,
  BFI_QUESTIONS,
  VIA_QUESTIONS,
  ASSESSMENT_INFO,
} from "../data/assessmentQuestions.js";
import {
  AssessmentStorage,
  AssessmentValidator,
  AssessmentProgress,
  AssessmentTimer,
} from "../utils/assessmentUtils.js";
import { Toast } from "../components/common/Toast.js";
import { Router } from "../utils/auth.js";

export class Assessment {
  constructor(container) {
    this.container = container;
    this.currentSection = "intro"; // 'intro', 'riasec', 'bfi', 'via', 'review', 'results'
    this.responses = {
      riasec: {},
      bfi: {},
      via: {},
    };
    this.timer = new AssessmentTimer();
    this.currentSectionComponent = null;

    this.initializeResponses();
    this.loadSavedProgress();
    this.render();
    this.attachEventListeners();
  }

  initializeResponses() {
    // Initialize empty response arrays for each dimension

    // RIASEC - 10 questions per dimension
    const riasecDimensions = [
      "realistic",
      "investigative",
      "artistic",
      "social",
      "enterprising",
      "conventional",
    ];
    riasecDimensions.forEach((dimension) => {
      this.responses.riasec[dimension] = new Array(10).fill(null);
    });

    // BFI - varying questions per dimension
    const bfiDimensions = {
      openness: 10,
      conscientiousness: 9,
      extraversion: 8,
      agreeableness: 9,
      neuroticism: 8,
    };
    Object.entries(bfiDimensions).forEach(([dimension, count]) => {
      this.responses.bfi[dimension] = new Array(count).fill(null);
    });

    // VIA - 4 questions per strength
    const viaStrengths = [
      "creativity",
      "curiosity",
      "judgment",
      "loveOfLearning",
      "perspective",
      "bravery",
      "perseverance",
      "honesty",
      "zest",
      "love",
      "kindness",
      "socialIntelligence",
      "teamwork",
      "fairness",
      "leadership",
      "forgiveness",
      "humility",
      "prudence",
      "selfRegulation",
      "appreciationOfBeauty",
      "gratitude",
      "hope",
      "humor",
      "spirituality",
    ];
    viaStrengths.forEach((strength) => {
      this.responses.via[strength] = new Array(4).fill(null);
    });
  }

  loadSavedProgress() {
    const savedProgress = AssessmentStorage.loadProgress();
    if (savedProgress) {
      this.responses = { ...this.responses, ...savedProgress.responses };
      this.currentSection = savedProgress.currentSection || "intro";

      // Show restore progress option
      this.showRestoreProgressDialog();
    }
  }

  showRestoreProgressDialog() {
    const progress = AssessmentProgress.calculateOverallProgress(
      this.responses
    );

    if (progress > 0) {
      const restore = confirm(
        `You have ${progress}% of the assessment completed from a previous session. ` +
          "Would you like to continue where you left off?"
      );

      if (!restore) {
        this.initializeResponses();
        this.currentSection = "intro";
        AssessmentStorage.clearProgress();
      }
    }
  }

  render() {
    this.container.innerHTML = `
      <div class="assessment-page min-h-screen bg-gray-50">
        <!-- Header -->
        <div class="bg-white shadow-sm border-b border-gray-200">
          <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
              <div class="flex items-center space-x-4">
                <h1 class="text-2xl font-bold text-gray-900">
                  AI-Driven Talent Mapping Assessment
                </h1>
                <div class="text-sm text-gray-500" id="timer-display">
                  Time: 00:00
                </div>
              </div>

              <div class="flex items-center space-x-4">
                <!-- Overall progress -->
                <div class="flex items-center space-x-2">
                  <span class="text-sm text-gray-600">Overall Progress:</span>
                  <div class="w-32 bg-gray-200 rounded-full h-2">
                    <div
                      id="overall-progress-bar"
                      class="bg-green-600 h-2 rounded-full transition-all duration-300"
                      style="width: 0%"
                    ></div>
                  </div>
                  <span class="text-sm font-medium text-gray-900" id="overall-progress-text">0%</span>
                </div>

                <!-- Save progress button -->
                <button
                  id="save-progress"
                  class="text-sm text-blue-600 hover:text-blue-800 font-medium"
                >
                  Save Progress
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Section navigation -->
        <div class="bg-white border-b border-gray-200" id="section-nav">
          <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <nav class="flex space-x-8 py-4">
              ${this.renderSectionNavigation()}
            </nav>
          </div>
        </div>

        <!-- Main content -->
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div id="section-content">
            <!-- Section content will be rendered here -->
          </div>
        </div>
      </div>
    `;

    this.renderCurrentSection();
    this.updateOverallProgress();
    this.startTimer();
  }

  renderSectionNavigation() {
    const sections = [
      { id: "intro", name: "Introduction", icon: "info" },
      { id: "riasec", name: "RIASEC (60)", icon: "work" },
      { id: "bfi", name: "Big Five (44)", icon: "person" },
      { id: "via", name: "Character Strengths (96)", icon: "star" },
      { id: "review", name: "Review", icon: "check" },
      { id: "results", name: "Results", icon: "chart" },
    ];

    return sections
      .map((section) => {
        const isActive = this.currentSection === section.id;
        const isCompleted = this.isSectionCompleted(section.id);
        const isAccessible = this.isSectionAccessible(section.id);

        return `
        <button
          class="section-nav-item flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors
            ${
              isActive
                ? "bg-blue-100 text-blue-700"
                : isCompleted
                ? "text-green-600 hover:text-green-800"
                : isAccessible
                ? "text-gray-600 hover:text-gray-800"
                : "text-gray-400 cursor-not-allowed"
            }"
          data-section="${section.id}"
          ${!isAccessible ? "disabled" : ""}
        >
          ${this.getSectionIcon(section.icon, isCompleted)}
          <span>${section.name}</span>
          ${
            isCompleted
              ? '<svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>'
              : ""
          }
        </button>
      `;
      })
      .join("");
  }

  getSectionIcon(iconType, isCompleted) {
    if (isCompleted) {
      return '<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>';
    }

    const icons = {
      info: '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>',
      work: '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0H8m8 0v2a2 2 0 01-2 2H10a2 2 0 01-2-2V6"></path></svg>',
      person:
        '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path></svg>',
      star: '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path></svg>',
      check:
        '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>',
      chart:
        '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg>',
    };

    return icons[iconType] || icons.info;
  }

  renderCurrentSection() {
    const sectionContent = this.container.querySelector("#section-content");

    // Clean up previous section component
    if (this.currentSectionComponent) {
      this.currentSectionComponent.destroy();
      this.currentSectionComponent = null;
    }

    switch (this.currentSection) {
      case "intro":
        this.renderIntroSection(sectionContent);
        break;
      case "riasec":
        this.renderAssessmentSection(
          sectionContent,
          "riasec",
          RIASEC_QUESTIONS,
          ASSESSMENT_INFO.riasec
        );
        break;
      case "bfi":
        this.renderAssessmentSection(
          sectionContent,
          "bfi",
          BFI_QUESTIONS,
          ASSESSMENT_INFO.bfi
        );
        break;
      case "via":
        this.renderAssessmentSection(
          sectionContent,
          "via",
          VIA_QUESTIONS,
          ASSESSMENT_INFO.via
        );
        break;
      case "review":
        this.renderReviewSection(sectionContent);
        break;
      case "results":
        this.renderResultsSection(sectionContent);
        break;
    }
  }

  renderIntroSection(container) {
    container.innerHTML = `
      <div class="max-w-4xl mx-auto">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          <div class="text-center mb-8">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">
              Welcome to the AI-Driven Talent Mapping Assessment
            </h2>
            <p class="text-lg text-gray-600">
              This comprehensive assessment will help identify your career interests, personality traits, and character strengths.
            </p>
          </div>

          <div class="grid md:grid-cols-3 gap-6 mb-8">
            <div class="text-center p-6 bg-blue-50 rounded-lg">
              <div class="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0H8m8 0v2a2 2 0 01-2 2H10a2 2 0 01-2-2V6"></path>
                </svg>
              </div>
              <h3 class="text-lg font-semibold text-gray-900 mb-2">RIASEC Assessment</h3>
              <p class="text-sm text-gray-600">60 questions exploring your work interests across 6 career types</p>
            </div>

            <div class="text-center p-6 bg-green-50 rounded-lg">
              <div class="w-12 h-12 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
              </div>
              <h3 class="text-lg font-semibold text-gray-900 mb-2">Big Five Personality</h3>
              <p class="text-sm text-gray-600">44 questions assessing your personality across 5 major dimensions</p>
            </div>

            <div class="text-center p-6 bg-purple-50 rounded-lg">
              <div class="w-12 h-12 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                </svg>
              </div>
              <h3 class="text-lg font-semibold text-gray-900 mb-2">Character Strengths</h3>
              <p class="text-sm text-gray-600">96 questions identifying your top character strengths across 24 dimensions</p>
            </div>
          </div>

          <div class="bg-yellow-50 border border-yellow-200 rounded-md p-6 mb-8">
            <div class="flex items-start space-x-3">
              <svg class="w-6 h-6 text-yellow-600 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
              </svg>
              <div>
                <h4 class="text-lg font-medium text-yellow-900 mb-2">Important Instructions</h4>
                <ul class="text-sm text-yellow-800 space-y-1">
                  <li>• Total assessment time: Approximately 30-45 minutes</li>
                  <li>• Answer all questions honestly - there are no right or wrong answers</li>
                  <li>• Use the 1-7 scale where 1 = Strongly Disagree and 7 = Strongly Agree</li>
                  <li>• Your progress is automatically saved</li>
                  <li>• You can navigate between sections and change your answers</li>
                </ul>
              </div>
            </div>
          </div>

          <div class="text-center">
            <button
              id="start-assessment"
              class="btn-primary px-8 py-3 text-lg font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Start Assessment
              <svg class="w-5 h-5 ml-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>
    `;

    // Attach event listener for start button
    const startButton = container.querySelector("#start-assessment");
    startButton?.addEventListener("click", () => {
      this.navigateToSection("riasec");
    });
  }

  renderAssessmentSection(container, sectionType, questionData, sectionInfo) {
    this.currentSectionComponent = new AssessmentSection(container, {
      sectionType,
      sectionData: questionData,
      responses: this.responses[sectionType],
      onResponseChange: (dimension, index, value) => {
        this.handleResponseChange(sectionType, dimension, index, value);
      },
      onSectionComplete: () => {
        this.handleSectionComplete(sectionType);
      },
    });
  }

  handleResponseChange(sectionType, dimension, index, value) {
    // Update response
    if (!this.responses[sectionType][dimension]) {
      this.responses[sectionType][dimension] = [];
    }
    this.responses[sectionType][dimension][index] = value;

    // Save progress
    this.saveProgress();
    this.updateOverallProgress();
  }

  handleSectionComplete(sectionType) {
    Toast.show(`${sectionType.toUpperCase()} section completed!`, "success");

    // Auto-navigate to next section after a delay
    setTimeout(() => {
      const nextSection = this.getNextSection(sectionType);
      if (nextSection) {
        this.navigateToSection(nextSection);
      }
    }, 1500);
  }

  getNextSection(currentSection) {
    const sectionOrder = ["intro", "riasec", "bfi", "via", "review", "results"];
    const currentIndex = sectionOrder.indexOf(currentSection);
    return currentIndex < sectionOrder.length - 1
      ? sectionOrder[currentIndex + 1]
      : null;
  }

  navigateToSection(sectionId) {
    if (!this.isSectionAccessible(sectionId)) {
      Toast.show("Please complete previous sections first", "error");
      return;
    }

    this.currentSection = sectionId;
    this.renderCurrentSection();
    this.updateSectionNavigation();
    this.saveProgress();

    // Scroll to top
    window.scrollTo({ top: 0, behavior: "smooth" });
  }

  isSectionAccessible(sectionId) {
    const sectionOrder = ["intro", "riasec", "bfi", "via", "review", "results"];
    const targetIndex = sectionOrder.indexOf(sectionId);

    if (targetIndex <= 0) return true; // intro is always accessible

    // Check if previous sections are completed
    for (let i = 1; i < targetIndex; i++) {
      if (!this.isSectionCompleted(sectionOrder[i])) {
        return false;
      }
    }

    return true;
  }

  isSectionCompleted(sectionId) {
    if (sectionId === "intro") return true;
    if (sectionId === "results") return this.isSectionCompleted("review");

    const progress = AssessmentProgress.calculateSectionProgress(
      sectionId,
      this.responses[sectionId] || {}
    );
    return progress.isComplete;
  }

  updateSectionNavigation() {
    const navContainer = this.container.querySelector("#section-nav nav");
    if (navContainer) {
      navContainer.innerHTML = this.renderSectionNavigation();
      this.attachSectionNavigationListeners();
    }
  }

  attachEventListeners() {
    // Save progress button
    const saveButton = this.container.querySelector("#save-progress");
    saveButton?.addEventListener("click", () => {
      this.saveProgress();
      Toast.show("Progress saved successfully!", "success");
    });

    // Section navigation
    this.attachSectionNavigationListeners();
  }

  attachSectionNavigationListeners() {
    const navItems = this.container.querySelectorAll(".section-nav-item");
    navItems.forEach((item) => {
      item.addEventListener("click", (e) => {
        const sectionId = e.currentTarget.dataset.section;
        if (sectionId && !e.currentTarget.disabled) {
          this.navigateToSection(sectionId);
        }
      });
    });
  }

  saveProgress() {
    AssessmentStorage.saveProgress({
      currentSection: this.currentSection,
      responses: this.responses,
      timestamp: new Date().toISOString(),
    });
  }

  updateOverallProgress() {
    const progress = AssessmentProgress.calculateOverallProgress(
      this.responses
    );

    const progressBar = this.container.querySelector("#overall-progress-bar");
    const progressText = this.container.querySelector("#overall-progress-text");

    if (progressBar) {
      progressBar.style.width = `${progress}%`;
    }

    if (progressText) {
      progressText.textContent = `${progress}%`;
    }
  }

  startTimer() {
    this.timer.start();

    // Update timer display every second
    this.timerInterval = setInterval(() => {
      const timerDisplay = this.container.querySelector("#timer-display");
      if (timerDisplay) {
        timerDisplay.textContent = `Time: ${this.timer.getFormattedElapsedTime()}`;
      }
    }, 1000);
  }

  renderReviewSection(container) {
    const validation = AssessmentValidator.validateCompleteAssessment(
      this.responses
    );
    const overallProgress = AssessmentProgress.calculateOverallProgress(
      this.responses
    );

    container.innerHTML = `
      <div class="max-w-4xl mx-auto">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          <div class="text-center mb-8">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">
              Review Your Assessment
            </h2>
            <p class="text-lg text-gray-600">
              Please review your responses before submitting for AI analysis.
            </p>
          </div>

          <!-- Progress Summary -->
          <div class="grid md:grid-cols-3 gap-6 mb-8">
            ${this.renderSectionSummary("riasec", "RIASEC Holland Codes", 60)}
            ${this.renderSectionSummary("bfi", "Big Five Personality", 44)}
            ${this.renderSectionSummary("via", "Character Strengths", 96)}
          </div>

          <!-- Validation Status -->
          <div class="mb-8">
            ${
              validation.isValid
                ? `
              <div class="bg-green-50 border border-green-200 rounded-md p-6">
                <div class="flex items-center space-x-3">
                  <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                  </svg>
                  <div>
                    <h4 class="text-lg font-medium text-green-900">Assessment Complete!</h4>
                    <p class="text-sm text-green-800 mt-1">All questions have been answered. You're ready to submit for AI analysis.</p>
                  </div>
                </div>
              </div>
            `
                : `
              <div class="bg-red-50 border border-red-200 rounded-md p-6">
                <div class="flex items-start space-x-3">
                  <svg class="w-6 h-6 text-red-600 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                  </svg>
                  <div>
                    <h4 class="text-lg font-medium text-red-900">Incomplete Assessment</h4>
                    <p class="text-sm text-red-800 mt-1">Please complete all sections before submitting:</p>
                    <ul class="text-sm text-red-800 mt-2 space-y-1">
                      ${validation.errors
                        .slice(0, 5)
                        .map((error) => `<li>• ${error}</li>`)
                        .join("")}
                      ${
                        validation.errors.length > 5
                          ? `<li>• ... and ${
                              validation.errors.length - 5
                            } more issues</li>`
                          : ""
                      }
                    </ul>
                  </div>
                </div>
              </div>
            `
            }
          </div>

          <!-- Submit Section -->
          <div class="text-center">
            <div class="mb-6">
              <p class="text-sm text-gray-600 mb-4">
                By submitting this assessment, you agree to have your responses analyzed by our AI system
                to generate personalized career and development insights.
              </p>
              <div class="text-xs text-gray-500">
                Estimated processing time: 2-5 minutes
              </div>
            </div>

            <div class="flex justify-center space-x-4">
              <button
                id="back-to-sections"
                class="btn-secondary px-6 py-3 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Back to Sections
              </button>

              <button
                id="submit-assessment"
                class="btn-primary px-8 py-3 text-lg font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                ${!validation.isValid ? "disabled" : ""}
              >
                <span id="submit-text">Submit for AI Analysis</span>
                <svg class="w-5 h-5 ml-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    `;

    // Attach event listeners
    const backButton = container.querySelector("#back-to-sections");
    const submitButton = container.querySelector("#submit-assessment");

    backButton?.addEventListener("click", () => {
      const nextIncomplete = AssessmentProgress.getNextIncompleteSection(
        this.responses
      );
      this.navigateToSection(nextIncomplete || "via");
    });

    submitButton?.addEventListener("click", () => {
      this.submitAssessment();
    });
  }

  renderSectionSummary(sectionType, title, totalQuestions) {
    const progress = AssessmentProgress.calculateSectionProgress(
      sectionType,
      this.responses[sectionType] || {}
    );
    const isComplete = progress.isComplete;

    return `
      <div class="text-center p-6 ${
        isComplete
          ? "bg-green-50 border-green-200"
          : "bg-gray-50 border-gray-200"
      } border rounded-lg">
        <div class="w-12 h-12 ${
          isComplete ? "bg-green-600" : "bg-gray-400"
        } rounded-full flex items-center justify-center mx-auto mb-4">
          ${
            isComplete
              ? '<svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>'
              : '<svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>'
          }
        </div>
        <h3 class="text-lg font-semibold text-gray-900 mb-2">${title}</h3>
        <div class="text-sm text-gray-600 mb-3">
          ${progress.answeredQuestions} / ${totalQuestions} questions
        </div>
        <div class="w-full bg-gray-200 rounded-full h-2">
          <div
            class="${
              isComplete ? "bg-green-600" : "bg-blue-600"
            } h-2 rounded-full transition-all duration-300"
            style="width: ${progress.percentage}%"
          ></div>
        </div>
        <div class="text-xs text-gray-500 mt-2">${
          progress.percentage
        }% complete</div>
      </div>
    `;
  }

  async submitAssessment() {
    const submitButton = this.container.querySelector("#submit-assessment");
    const submitText = this.container.querySelector("#submit-text");

    if (!submitButton || !submitText) return;

    // Show loading state
    submitButton.disabled = true;
    submitText.textContent = "Submitting...";
    submitButton.innerHTML = `
      <span>Submitting...</span>
      <svg class="animate-spin w-5 h-5 ml-2 inline" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
    `;

    try {
      // Process assessment data
      const assessmentData = AssessmentService.processAssessmentData(
        this.responses,
        { bfi: BFI_QUESTIONS }
      );

      // Submit to API
      const response = await AssessmentService.submitAssessment(assessmentData);

      if (response.success) {
        // Store job ID and navigate to results
        this.jobId = response.data.jobId;
        this.assessmentResult = response.data;

        // Clear saved progress
        AssessmentStorage.clearProgress();

        // Navigate to results
        this.navigateToSection("results");

        Toast.show("Assessment submitted successfully!", "success");
      } else {
        throw new Error(response.message || "Submission failed");
      }
    } catch (error) {
      console.error("Assessment submission failed:", error);
      Toast.show(`Submission failed: ${error.message}`, "error");

      // Reset button state
      submitButton.disabled = false;
      submitText.textContent = "Submit for AI Analysis";
      submitButton.innerHTML = `
        <span>Submit for AI Analysis</span>
        <svg class="w-5 h-5 ml-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
        </svg>
      `;
    }
  }

  renderResultsSection(container) {
    if (!this.jobId) {
      container.innerHTML = `
        <div class="max-w-4xl mx-auto text-center">
          <div class="bg-yellow-50 border border-yellow-200 rounded-md p-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-4">No Assessment Results</h2>
            <p class="text-gray-600 mb-6">Please complete and submit the assessment first.</p>
            <button
              onclick="window.location.reload()"
              class="btn-primary px-6 py-3 text-white bg-blue-600 rounded-md hover:bg-blue-700"
            >
              Start New Assessment
            </button>
          </div>
        </div>
      `;
      return;
    }

    container.innerHTML = `
      <div class="max-w-4xl mx-auto">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          <div class="text-center mb-8">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">
              Assessment Results
            </h2>
            <p class="text-lg text-gray-600">
              Your assessment is being processed by our AI system.
            </p>
          </div>

          <div id="results-content">
            <!-- Results will be loaded here -->
          </div>
        </div>
      </div>
    `;

    // Start polling for results
    this.pollForResults();
  }

  async pollForResults() {
    const resultsContent = this.container.querySelector("#results-content");
    if (!resultsContent || !this.jobId) return;

    // Show initial loading state
    resultsContent.innerHTML = `
      <div class="text-center py-12">
        <div class="animate-spin w-16 h-16 border-4 border-blue-600 border-t-transparent rounded-full mx-auto mb-6"></div>
        <h3 class="text-xl font-semibold text-gray-900 mb-2">Processing Your Assessment</h3>
        <p class="text-gray-600 mb-4">Our AI is analyzing your responses...</p>
        <div class="text-sm text-gray-500">
          Job ID: ${this.jobId}
        </div>
      </div>
    `;

    try {
      const pollInterval = setInterval(async () => {
        try {
          const statusResponse = await AssessmentService.getAssessmentStatus(
            this.jobId
          );

          if (statusResponse.success) {
            const status = statusResponse.data.status;

            if (status === "completed") {
              clearInterval(pollInterval);
              this.displayResults(statusResponse.data);
            } else if (status === "failed") {
              clearInterval(pollInterval);
              this.displayError(
                "Assessment processing failed. Please try again."
              );
            } else {
              // Update loading state with queue info
              this.updateLoadingState(statusResponse.data);
            }
          }
        } catch (error) {
          console.error("Error polling for results:", error);
          // Continue polling unless it's a critical error
        }
      }, 5000); // Poll every 5 seconds

      // Stop polling after 10 minutes
      setTimeout(() => {
        clearInterval(pollInterval);
        this.displayError(
          "Assessment processing is taking longer than expected. Please check back later."
        );
      }, 600000);
    } catch (error) {
      console.error("Error starting result polling:", error);
      this.displayError("Failed to check assessment status. Please try again.");
    }
  }

  updateLoadingState(statusData) {
    const resultsContent = this.container.querySelector("#results-content");
    if (!resultsContent) return;

    resultsContent.innerHTML = `
      <div class="text-center py-12">
        <div class="animate-spin w-16 h-16 border-4 border-blue-600 border-t-transparent rounded-full mx-auto mb-6"></div>
        <h3 class="text-xl font-semibold text-gray-900 mb-2">Processing Your Assessment</h3>
        <p class="text-gray-600 mb-4">Status: ${
          statusData.status || "Processing"
        }</p>
        ${
          statusData.queuePosition
            ? `
          <div class="text-sm text-gray-500 mb-2">
            Queue Position: ${statusData.queuePosition}
          </div>
        `
            : ""
        }
        ${
          statusData.estimatedProcessingTime
            ? `
          <div class="text-sm text-gray-500 mb-4">
            Estimated Time: ${statusData.estimatedProcessingTime}
          </div>
        `
            : ""
        }
        <div class="text-xs text-gray-400">
          Job ID: ${this.jobId}
        </div>
      </div>
    `;
  }

  displayResults(resultData) {
    const resultsContent = this.container.querySelector("#results-content");
    if (!resultsContent) return;

    resultsContent.innerHTML = `
      <div class="text-center py-12">
        <div class="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-6">
          <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
          </svg>
        </div>
        <h3 class="text-xl font-semibold text-gray-900 mb-4">Assessment Complete!</h3>
        <p class="text-gray-600 mb-6">Your personalized career insights are ready.</p>

        <div class="bg-blue-50 border border-blue-200 rounded-md p-6 mb-6">
          <h4 class="text-lg font-medium text-blue-900 mb-2">What's Next?</h4>
          <p class="text-sm text-blue-800">
            Your results have been processed and are available in your dashboard.
            You'll receive detailed insights about your career interests, personality traits, and character strengths.
          </p>
        </div>

        <div class="flex justify-center space-x-4">
          <button
            onclick="window.location.href = '/dashboard'"
            class="btn-primary px-6 py-3 text-white bg-blue-600 rounded-md hover:bg-blue-700"
          >
            View Results in Dashboard
          </button>

          <button
            onclick="window.location.reload()"
            class="btn-secondary px-6 py-3 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          >
            Take Another Assessment
          </button>
        </div>
      </div>
    `;
  }

  displayError(message) {
    const resultsContent = this.container.querySelector("#results-content");
    if (!resultsContent) return;

    resultsContent.innerHTML = `
      <div class="text-center py-12">
        <div class="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-6">
          <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
          </svg>
        </div>
        <h3 class="text-xl font-semibold text-gray-900 mb-4">Processing Error</h3>
        <p class="text-gray-600 mb-6">${message}</p>

        <div class="flex justify-center space-x-4">
          <button
            onclick="window.location.reload()"
            class="btn-primary px-6 py-3 text-white bg-blue-600 rounded-md hover:bg-blue-700"
          >
            Try Again
          </button>

          <button
            onclick="window.location.href = '/dashboard'"
            class="btn-secondary px-6 py-3 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          >
            Go to Dashboard
          </button>
        </div>
      </div>
    `;
  }

  destroy() {
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
    }

    if (this.currentSectionComponent) {
      this.currentSectionComponent.destroy();
    }

    this.timer.end();
    this.container.innerHTML = "";
  }
}
