import { LIKERT_SCALE } from '../../data/assessmentQuestions.js';

export class LikertScale {
  constructor(container, options = {}) {
    this.container = container;
    this.options = {
      name: options.name || 'likert',
      value: options.value || null,
      onChange: options.onChange || (() => {}),
      disabled: options.disabled || false,
      required: options.required !== false, // default to true
      ...options
    };
    this.render();
    this.attachEventListeners();
  }

  render() {
    const { name, value, disabled, required } = this.options;
    
    this.container.innerHTML = `
      <div class="likert-scale">
        <div class="flex flex-col space-y-3">
          <!-- Scale labels -->
          <div class="flex justify-between text-xs text-gray-600 px-2">
            <span>Strongly Disagree</span>
            <span>Neutral</span>
            <span>Strongly Agree</span>
          </div>
          
          <!-- Radio buttons -->
          <div class="flex justify-between items-center bg-gray-50 rounded-lg p-4">
            ${Object.entries(LIKERT_SCALE).map(([scaleValue, label]) => `
              <div class="flex flex-col items-center space-y-2">
                <input 
                  type="radio" 
                  id="${name}_${scaleValue}" 
                  name="${name}" 
                  value="${scaleValue}"
                  class="w-5 h-5 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 focus:ring-2"
                  ${value == scaleValue ? 'checked' : ''}
                  ${disabled ? 'disabled' : ''}
                  ${required ? 'required' : ''}
                >
                <label 
                  for="${name}_${scaleValue}" 
                  class="text-sm font-medium text-gray-700 cursor-pointer select-none text-center"
                >
                  ${scaleValue}
                </label>
                <span class="text-xs text-gray-500 text-center max-w-16 leading-tight">
                  ${this.getShortLabel(scaleValue)}
                </span>
              </div>
            `).join('')}
          </div>
          
          <!-- Full scale reference -->
          <div class="text-xs text-gray-500 space-y-1">
            <div class="font-medium">Scale Reference:</div>
            <div class="grid grid-cols-2 gap-x-4 gap-y-1">
              ${Object.entries(LIKERT_SCALE).map(([scaleValue, label]) => `
                <div class="flex">
                  <span class="font-medium w-4">${scaleValue}:</span>
                  <span class="ml-2">${label}</span>
                </div>
              `).join('')}
            </div>
          </div>
        </div>
      </div>
    `;
  }

  getShortLabel(value) {
    const shortLabels = {
      1: "Strongly Disagree",
      2: "Disagree", 
      3: "Slightly Disagree",
      4: "Neutral",
      5: "Slightly Agree",
      6: "Agree",
      7: "Strongly Agree"
    };
    return shortLabels[value] || '';
  }

  attachEventListeners() {
    const radioButtons = this.container.querySelectorAll(`input[name="${this.options.name}"]`);
    
    radioButtons.forEach(radio => {
      radio.addEventListener('change', (e) => {
        if (e.target.checked) {
          const value = parseInt(e.target.value);
          this.options.onChange(value);
          this.highlightSelection(value);
        }
      });
    });
  }

  highlightSelection(selectedValue) {
    // Add visual feedback for selected option
    const radioButtons = this.container.querySelectorAll(`input[name="${this.options.name}"]`);
    
    radioButtons.forEach(radio => {
      const container = radio.closest('.flex.flex-col.items-center');
      if (radio.value == selectedValue) {
        container.classList.add('bg-blue-100', 'rounded-lg', 'p-2', '-m-2');
        container.classList.remove('bg-transparent');
      } else {
        container.classList.remove('bg-blue-100', 'rounded-lg', 'p-2', '-m-2');
        container.classList.add('bg-transparent');
      }
    });
  }

  getValue() {
    const checkedRadio = this.container.querySelector(`input[name="${this.options.name}"]:checked`);
    return checkedRadio ? parseInt(checkedRadio.value) : null;
  }

  setValue(value) {
    const radio = this.container.querySelector(`input[name="${this.options.name}"][value="${value}"]`);
    if (radio) {
      radio.checked = true;
      this.highlightSelection(value);
    }
  }

  setDisabled(disabled) {
    const radioButtons = this.container.querySelectorAll(`input[name="${this.options.name}"]`);
    radioButtons.forEach(radio => {
      radio.disabled = disabled;
    });
    this.options.disabled = disabled;
  }

  isValid() {
    if (!this.options.required) return true;
    return this.getValue() !== null;
  }

  showError(message) {
    this.clearError();
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message text-red-600 text-sm mt-2';
    errorDiv.textContent = message;
    this.container.appendChild(errorDiv);
  }

  clearError() {
    const existingError = this.container.querySelector('.error-message');
    if (existingError) {
      existingError.remove();
    }
  }

  destroy() {
    // Clean up event listeners and DOM
    this.container.innerHTML = '';
  }
}
