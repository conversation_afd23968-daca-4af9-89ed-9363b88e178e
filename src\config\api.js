import { <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "./env.js";

// API Configuration
export const API_CONFIG = {
  BASE_URL: ENV.API_BASE_URL,
  ENDPOINTS: {
    AUTH: {
      REGISTER: "/api/auth/register",
      LOGIN: "/api/auth/login",
      PROFILE: "/api/auth/profile",
      LOGOUT: "/api/auth/logout",
      CHANGE_PASSWORD: "/api/auth/change-password",
      DELETE_ACCOUNT: "/api/auth/account",
    },
    ASSESSMENT: {
      SUBMIT: "/api/assessment/submit",
      STATUS: "/api/assessment/status/:jobId",
      QUEUE_STATUS: "/api/assessment/queue/status",
    },
  },
};

// HTTP Client
export class ApiClient {
  constructor() {
    this.baseURL = API_CONFIG.BASE_URL;
  }

  getAuthHeaders() {
    const token = localStorage.getItem(ENV.STORAGE_KEYS.TOKEN);
    return token ? { Authorization: `Bearer ${token}` } : {};
  }

  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      headers: {
        "Content-Type": "application/json",
        ...this.getAuthHeaders(),
        ...options.headers,
      },
      ...options,
    };

    try {
      Logger.log("API Request:", config.method || "GET", url);
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        Logger.error("API Request failed:", response.status, data);
        throw new Error(data.message || "Request failed");
      }

      Logger.log("API Response:", data);
      return data;
    } catch (error) {
      Logger.error("API Request failed:", error);
      throw error;
    }
  }

  async get(endpoint, options = {}) {
    return this.request(endpoint, { method: "GET", ...options });
  }

  async post(endpoint, body, options = {}) {
    return this.request(endpoint, {
      method: "POST",
      body: JSON.stringify(body),
      ...options,
    });
  }

  async put(endpoint, body, options = {}) {
    return this.request(endpoint, {
      method: "PUT",
      body: JSON.stringify(body),
      ...options,
    });
  }

  async delete(endpoint, options = {}) {
    return this.request(endpoint, { method: "DELETE", ...options });
  }
}

export const apiClient = new ApiClient();
