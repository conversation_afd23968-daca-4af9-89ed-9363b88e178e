import { QuestionCard } from './QuestionCard.js';

export class AssessmentSection {
  constructor(container, options = {}) {
    this.container = container;
    this.options = {
      sectionType: options.sectionType || 'riasec', // 'riasec', 'bfi', 'via'
      sectionData: options.sectionData || {},
      responses: options.responses || {},
      onResponseChange: options.onResponseChange || (() => {}),
      onSectionComplete: options.onSectionComplete || (() => {}),
      disabled: options.disabled || false,
      ...options
    };
    
    this.questionCards = [];
    this.currentQuestionIndex = 0;
    this.render();
    this.initializeQuestions();
  }

  render() {
    const { sectionType, sectionData } = this.options;
    
    this.container.innerHTML = `
      <div class="assessment-section">
        <!-- Section header -->
        <div class="section-header bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <div class="flex items-center justify-between mb-4">
            <h2 class="text-2xl font-bold text-gray-900">
              ${sectionData.title || 'Assessment Section'}
            </h2>
            <div class="flex items-center space-x-4">
              <div class="text-sm text-gray-500">
                <span class="font-medium">${this.getTotalQuestions()}</span> questions
              </div>
              <div class="progress-indicator">
                <div class="text-sm font-medium text-blue-600">
                  <span id="completed-count">0</span> / ${this.getTotalQuestions()} completed
                </div>
              </div>
            </div>
          </div>
          
          <p class="text-gray-600 mb-4">
            ${sectionData.description || ''}
          </p>
          
          <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
            <div class="flex items-start space-x-3">
              <svg class="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
              </svg>
              <div>
                <h4 class="text-sm font-medium text-blue-900 mb-1">Instructions</h4>
                <p class="text-sm text-blue-800">
                  ${sectionData.instruction || 'Please answer all questions honestly.'}
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Questions container -->
        <div class="questions-container">
          <!-- Questions will be rendered here -->
        </div>

        <!-- Navigation -->
        <div class="section-navigation bg-white rounded-lg shadow-sm border border-gray-200 p-6 mt-6">
          <div class="flex justify-between items-center">
            <button 
              id="prev-question" 
              class="btn-secondary px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              disabled
            >
              <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
              </svg>
              Previous
            </button>
            
            <div class="flex items-center space-x-4">
              <div class="text-sm text-gray-500">
                Question <span id="current-question">1</span> of ${this.getTotalQuestions()}
              </div>
              
              <!-- Progress bar -->
              <div class="w-32 bg-gray-200 rounded-full h-2">
                <div 
                  id="progress-bar"
                  class="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                  style="width: 0%"
                ></div>
              </div>
            </div>
            
            <button 
              id="next-question" 
              class="btn-primary px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
              <svg class="w-4 h-4 ml-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>
    `;

    this.attachNavigationListeners();
  }

  initializeQuestions() {
    const questionsContainer = this.container.querySelector('.questions-container');
    const { sectionType, responses } = this.options;
    
    // Clear existing questions
    this.questionCards = [];
    questionsContainer.innerHTML = '';

    const questions = this.getQuestionsForSection();
    let questionNumber = 1;

    questions.forEach((questionData, index) => {
      const questionContainer = document.createElement('div');
      questionContainer.className = 'question-wrapper';
      questionContainer.style.display = index === 0 ? 'block' : 'none';
      
      const questionCard = new QuestionCard(questionContainer, {
        questionNumber: questionNumber++,
        totalQuestions: this.getTotalQuestions(),
        questionText: questionData.text || questionData,
        questionId: `${sectionType}_${questionData.dimension}_${questionData.index}`,
        value: this.getResponseValue(questionData),
        prefix: sectionType === 'bfi' ? 'I see myself as someone who...' : '',
        isReverse: questionData.reverse || false,
        disabled: this.options.disabled,
        onChange: (value) => {
          this.handleResponseChange(questionData, value);
        }
      });

      this.questionCards.push(questionCard);
      questionsContainer.appendChild(questionContainer);
    });

    this.updateProgress();
  }

  getQuestionsForSection() {
    const { sectionType, sectionData } = this.options;
    const questions = [];

    if (sectionType === 'riasec') {
      Object.entries(sectionData).forEach(([dimension, dimensionQuestions]) => {
        if (Array.isArray(dimensionQuestions)) {
          dimensionQuestions.forEach((question, index) => {
            questions.push({
              text: question,
              dimension,
              index,
              reverse: false
            });
          });
        }
      });
    } else if (sectionType === 'bfi') {
      Object.entries(sectionData).forEach(([dimension, dimensionQuestions]) => {
        if (Array.isArray(dimensionQuestions)) {
          dimensionQuestions.forEach((questionData, index) => {
            questions.push({
              text: questionData.text,
              dimension,
              index,
              reverse: questionData.reverse
            });
          });
        }
      });
    } else if (sectionType === 'via') {
      Object.entries(sectionData).forEach(([strength, strengthQuestions]) => {
        if (Array.isArray(strengthQuestions)) {
          strengthQuestions.forEach((question, index) => {
            questions.push({
              text: question,
              dimension: strength,
              index,
              reverse: false
            });
          });
        }
      });
    }

    return questions;
  }

  getResponseValue(questionData) {
    const { responses } = this.options;
    const dimensionResponses = responses[questionData.dimension] || [];
    return dimensionResponses[questionData.index] || null;
  }

  handleResponseChange(questionData, value) {
    this.options.onResponseChange(questionData.dimension, questionData.index, value);
    this.updateProgress();
    
    // Auto-advance to next question after a short delay
    setTimeout(() => {
      if (this.currentQuestionIndex < this.questionCards.length - 1) {
        this.showNextQuestion();
      }
    }, 500);
  }

  getTotalQuestions() {
    const { sectionType, sectionData } = this.options;
    
    if (sectionType === 'riasec') {
      return Object.values(sectionData).reduce((total, questions) => total + (questions?.length || 0), 0);
    } else if (sectionType === 'bfi') {
      return Object.values(sectionData).reduce((total, questions) => total + (questions?.length || 0), 0);
    } else if (sectionType === 'via') {
      return Object.values(sectionData).reduce((total, questions) => total + (questions?.length || 0), 0);
    }
    
    return 0;
  }

  getCompletedCount() {
    const { responses } = this.options;
    let completed = 0;
    
    Object.values(responses).forEach(dimensionResponses => {
      if (Array.isArray(dimensionResponses)) {
        completed += dimensionResponses.filter(response => response !== null && response !== undefined).length;
      }
    });
    
    return completed;
  }

  updateProgress() {
    const completedCount = this.getCompletedCount();
    const totalQuestions = this.getTotalQuestions();
    const progressPercentage = (completedCount / totalQuestions) * 100;

    // Update progress indicators
    const completedCountElement = this.container.querySelector('#completed-count');
    const progressBar = this.container.querySelector('#progress-bar');
    const currentQuestionElement = this.container.querySelector('#current-question');

    if (completedCountElement) {
      completedCountElement.textContent = completedCount;
    }
    
    if (progressBar) {
      progressBar.style.width = `${progressPercentage}%`;
    }
    
    if (currentQuestionElement) {
      currentQuestionElement.textContent = this.currentQuestionIndex + 1;
    }

    // Update navigation buttons
    this.updateNavigationButtons();

    // Check if section is complete
    if (completedCount === totalQuestions) {
      this.options.onSectionComplete();
    }
  }

  attachNavigationListeners() {
    const prevButton = this.container.querySelector('#prev-question');
    const nextButton = this.container.querySelector('#next-question');

    prevButton?.addEventListener('click', () => {
      this.showPreviousQuestion();
    });

    nextButton?.addEventListener('click', () => {
      this.showNextQuestion();
    });
  }

  showPreviousQuestion() {
    if (this.currentQuestionIndex > 0) {
      this.questionCards[this.currentQuestionIndex].container.style.display = 'none';
      this.currentQuestionIndex--;
      this.questionCards[this.currentQuestionIndex].container.style.display = 'block';
      this.questionCards[this.currentQuestionIndex].scrollIntoView();
      this.updateProgress();
    }
  }

  showNextQuestion() {
    if (this.currentQuestionIndex < this.questionCards.length - 1) {
      this.questionCards[this.currentQuestionIndex].container.style.display = 'none';
      this.currentQuestionIndex++;
      this.questionCards[this.currentQuestionIndex].container.style.display = 'block';
      this.questionCards[this.currentQuestionIndex].scrollIntoView();
      this.updateProgress();
    }
  }

  updateNavigationButtons() {
    const prevButton = this.container.querySelector('#prev-question');
    const nextButton = this.container.querySelector('#next-question');

    if (prevButton) {
      prevButton.disabled = this.currentQuestionIndex === 0;
    }

    if (nextButton) {
      const isLastQuestion = this.currentQuestionIndex === this.questionCards.length - 1;
      nextButton.disabled = isLastQuestion;
      
      if (isLastQuestion) {
        nextButton.innerHTML = `
          Complete Section
          <svg class="w-4 h-4 ml-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
        `;
      } else {
        nextButton.innerHTML = `
          Next
          <svg class="w-4 h-4 ml-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
          </svg>
        `;
      }
    }
  }

  setDisabled(disabled) {
    this.options.disabled = disabled;
    this.questionCards.forEach(card => {
      card.setDisabled(disabled);
    });
  }

  destroy() {
    this.questionCards.forEach(card => {
      card.destroy();
    });
    this.questionCards = [];
    this.container.innerHTML = '';
  }
}
