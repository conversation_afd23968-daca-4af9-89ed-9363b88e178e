import { ENV } from '../config/env.js';

export class AssessmentStorage {
  static STORAGE_KEY = `${ENV.STORAGE_KEYS.TOKEN}_assessment_progress`;

  /**
   * Save assessment progress to localStorage
   * @param {Object} progressData - Assessment progress data
   */
  static saveProgress(progressData) {
    try {
      const dataToSave = {
        ...progressData,
        timestamp: new Date().toISOString(),
        version: '1.0'
      };
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(dataToSave));
    } catch (error) {
      console.error('Failed to save assessment progress:', error);
    }
  }

  /**
   * Load assessment progress from localStorage
   * @returns {Object|null} Saved progress data or null
   */
  static loadProgress() {
    try {
      const saved = localStorage.getItem(this.STORAGE_KEY);
      if (saved) {
        const data = JSON.parse(saved);
        // Check if data is not too old (24 hours)
        const savedTime = new Date(data.timestamp);
        const now = new Date();
        const hoursDiff = (now - savedTime) / (1000 * 60 * 60);
        
        if (hoursDiff < 24) {
          return data;
        } else {
          // Remove old data
          this.clearProgress();
        }
      }
    } catch (error) {
      console.error('Failed to load assessment progress:', error);
    }
    return null;
  }

  /**
   * Clear saved assessment progress
   */
  static clearProgress() {
    try {
      localStorage.removeItem(this.STORAGE_KEY);
    } catch (error) {
      console.error('Failed to clear assessment progress:', error);
    }
  }

  /**
   * Check if there is saved progress
   * @returns {boolean} True if there is saved progress
   */
  static hasProgress() {
    return this.loadProgress() !== null;
  }
}

export class AssessmentValidator {
  /**
   * Validate a single response value
   * @param {*} value - Response value to validate
   * @returns {boolean} True if valid
   */
  static isValidResponse(value) {
    return typeof value === 'number' && value >= 1 && value <= 7;
  }

  /**
   * Validate responses for a dimension
   * @param {Array} responses - Array of responses
   * @param {number} expectedCount - Expected number of responses
   * @returns {Object} Validation result
   */
  static validateDimension(responses, expectedCount) {
    const errors = [];
    
    if (!Array.isArray(responses)) {
      errors.push('Responses must be an array');
      return { isValid: false, errors };
    }

    if (responses.length !== expectedCount) {
      errors.push(`Expected ${expectedCount} responses, got ${responses.length}`);
    }

    responses.forEach((response, index) => {
      if (!this.isValidResponse(response)) {
        errors.push(`Invalid response at position ${index + 1}: ${response}`);
      }
    });

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate complete assessment responses
   * @param {Object} responses - All assessment responses
   * @returns {Object} Validation result
   */
  static validateCompleteAssessment(responses) {
    const errors = [];

    // Validate RIASEC responses
    const riasecDimensions = ['realistic', 'investigative', 'artistic', 'social', 'enterprising', 'conventional'];
    riasecDimensions.forEach(dimension => {
      const dimensionResponses = responses.riasec?.[dimension] || [];
      const validation = this.validateDimension(dimensionResponses, 10);
      if (!validation.isValid) {
        errors.push(`RIASEC ${dimension}: ${validation.errors.join(', ')}`);
      }
    });

    // Validate BFI responses
    const bfiDimensions = {
      openness: 10,
      conscientiousness: 9,
      extraversion: 8,
      agreeableness: 9,
      neuroticism: 8
    };
    
    Object.entries(bfiDimensions).forEach(([dimension, expectedCount]) => {
      const dimensionResponses = responses.bfi?.[dimension] || [];
      const validation = this.validateDimension(dimensionResponses, expectedCount);
      if (!validation.isValid) {
        errors.push(`BFI ${dimension}: ${validation.errors.join(', ')}`);
      }
    });

    // Validate VIA responses
    const viaStrengths = [
      'creativity', 'curiosity', 'judgment', 'loveOfLearning', 'perspective',
      'bravery', 'perseverance', 'honesty', 'zest', 'love', 'kindness',
      'socialIntelligence', 'teamwork', 'fairness', 'leadership', 'forgiveness',
      'humility', 'prudence', 'selfRegulation', 'appreciationOfBeauty',
      'gratitude', 'hope', 'humor', 'spirituality'
    ];
    
    viaStrengths.forEach(strength => {
      const strengthResponses = responses.via?.[strength] || [];
      const validation = this.validateDimension(strengthResponses, 4);
      if (!validation.isValid) {
        errors.push(`VIA ${strength}: ${validation.errors.join(', ')}`);
      }
    });

    return {
      isValid: errors.length === 0,
      errors,
      totalErrors: errors.length
    };
  }
}

export class AssessmentProgress {
  /**
   * Calculate overall progress percentage
   * @param {Object} responses - Current responses
   * @returns {number} Progress percentage (0-100)
   */
  static calculateOverallProgress(responses) {
    const totalQuestions = 60 + 44 + 96; // RIASEC + BFI + VIA
    let answeredQuestions = 0;

    // Count RIASEC responses
    if (responses.riasec) {
      Object.values(responses.riasec).forEach(dimensionResponses => {
        if (Array.isArray(dimensionResponses)) {
          answeredQuestions += dimensionResponses.filter(r => r !== null && r !== undefined).length;
        }
      });
    }

    // Count BFI responses
    if (responses.bfi) {
      Object.values(responses.bfi).forEach(dimensionResponses => {
        if (Array.isArray(dimensionResponses)) {
          answeredQuestions += dimensionResponses.filter(r => r !== null && r !== undefined).length;
        }
      });
    }

    // Count VIA responses
    if (responses.via) {
      Object.values(responses.via).forEach(strengthResponses => {
        if (Array.isArray(strengthResponses)) {
          answeredQuestions += strengthResponses.filter(r => r !== null && r !== undefined).length;
        }
      });
    }

    return Math.round((answeredQuestions / totalQuestions) * 100);
  }

  /**
   * Calculate section progress
   * @param {string} sectionType - 'riasec', 'bfi', or 'via'
   * @param {Object} sectionResponses - Responses for the section
   * @returns {Object} Progress information
   */
  static calculateSectionProgress(sectionType, sectionResponses) {
    let totalQuestions = 0;
    let answeredQuestions = 0;

    if (sectionType === 'riasec') {
      totalQuestions = 60;
      Object.values(sectionResponses).forEach(dimensionResponses => {
        if (Array.isArray(dimensionResponses)) {
          answeredQuestions += dimensionResponses.filter(r => r !== null && r !== undefined).length;
        }
      });
    } else if (sectionType === 'bfi') {
      totalQuestions = 44;
      Object.values(sectionResponses).forEach(dimensionResponses => {
        if (Array.isArray(dimensionResponses)) {
          answeredQuestions += dimensionResponses.filter(r => r !== null && r !== undefined).length;
        }
      });
    } else if (sectionType === 'via') {
      totalQuestions = 96;
      Object.values(sectionResponses).forEach(strengthResponses => {
        if (Array.isArray(strengthResponses)) {
          answeredQuestions += strengthResponses.filter(r => r !== null && r !== undefined).length;
        }
      });
    }

    return {
      totalQuestions,
      answeredQuestions,
      percentage: totalQuestions > 0 ? Math.round((answeredQuestions / totalQuestions) * 100) : 0,
      isComplete: answeredQuestions === totalQuestions
    };
  }

  /**
   * Get next incomplete section
   * @param {Object} responses - All responses
   * @returns {string|null} Next section to complete or null if all complete
   */
  static getNextIncompleteSection(responses) {
    const riasecProgress = this.calculateSectionProgress('riasec', responses.riasec || {});
    if (!riasecProgress.isComplete) return 'riasec';

    const bfiProgress = this.calculateSectionProgress('bfi', responses.bfi || {});
    if (!bfiProgress.isComplete) return 'bfi';

    const viaProgress = this.calculateSectionProgress('via', responses.via || {});
    if (!viaProgress.isComplete) return 'via';

    return null; // All sections complete
  }
}

export class AssessmentTimer {
  constructor() {
    this.startTime = null;
    this.endTime = null;
    this.pausedTime = 0;
    this.isPaused = false;
  }

  start() {
    this.startTime = new Date();
    this.isPaused = false;
  }

  pause() {
    if (!this.isPaused && this.startTime) {
      this.isPaused = true;
      this.pauseStartTime = new Date();
    }
  }

  resume() {
    if (this.isPaused && this.pauseStartTime) {
      this.pausedTime += new Date() - this.pauseStartTime;
      this.isPaused = false;
      this.pauseStartTime = null;
    }
  }

  end() {
    this.endTime = new Date();
    if (this.isPaused) {
      this.resume();
    }
  }

  getElapsedTime() {
    if (!this.startTime) return 0;
    
    const endTime = this.endTime || new Date();
    const currentPausedTime = this.isPaused ? 
      this.pausedTime + (new Date() - this.pauseStartTime) : 
      this.pausedTime;
    
    return endTime - this.startTime - currentPausedTime;
  }

  getFormattedElapsedTime() {
    const elapsed = this.getElapsedTime();
    const minutes = Math.floor(elapsed / 60000);
    const seconds = Math.floor((elapsed % 60000) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }
}
