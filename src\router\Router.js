import { Auth<PERSON>anager } from "../utils/auth.js";
import { LoginForm } from "../components/auth/LoginForm.js";
import { RegisterForm } from "../components/auth/RegisterForm.js";
import { Dashboard } from "../pages/Dashboard.js";
import { Profile } from "../pages/Profile.js";
import { Assessment } from "../pages/Assessment.js";
import { NotFound } from "../pages/NotFound.js";
import { Navbar } from "../components/layout/Navbar.js";

export class AppRouter {
  constructor() {
    this.routes = {
      "/": { component: Dashboard, requiresAuth: true },
      "/login": { component: LoginForm, requiresAuth: false },
      "/register": { component: RegisterForm, requiresAuth: false },
      "/dashboard": { component: Dashboard, requiresAuth: true },
      "/profile": { component: Profile, requiresAuth: true },
      "/assessment": { component: Assessment, requiresAuth: true },
    };

    this.appContainer = document.getElementById("app");
    this.navbarContainer = null;
    this.contentContainer = null;

    this.init();
  }

  init() {
    this.setupContainers();
    this.handleRoute();
    this.setupEventListeners();
  }

  setupContainers() {
    this.appContainer.innerHTML = `
      <div id="navbar-container"></div>
      <div id="content-container"></div>
    `;

    this.navbarContainer = document.getElementById("navbar-container");
    this.contentContainer = document.getElementById("content-container");
  }

  setupEventListeners() {
    // Listen for browser back/forward buttons
    window.addEventListener("popstate", () => {
      this.handleRoute();
    });

    // Listen for programmatic navigation
    window.addEventListener("navigate", (e) => {
      this.navigate(e.detail.path);
    });
  }

  getCurrentPath() {
    return window.location.pathname;
  }

  navigate(path) {
    window.history.pushState({}, "", path);
    this.handleRoute();
  }

  handleRoute() {
    const path = this.getCurrentPath();
    const route = this.routes[path];

    // Check authentication
    const isAuthenticated = AuthManager.isAuthenticated();

    // Handle 404 for unknown routes
    if (!route) {
      this.renderNavbar(isAuthenticated);
      this.renderComponent(NotFound);
      return;
    }

    if (route.requiresAuth && !isAuthenticated) {
      // Redirect to login if authentication required but user not logged in
      this.navigate("/login");
      return;
    }

    if (
      !route.requiresAuth &&
      isAuthenticated &&
      (path === "/login" || path === "/register")
    ) {
      // Redirect to dashboard if user is already logged in and trying to access auth pages
      this.navigate("/dashboard");
      return;
    }

    // Render navbar (only for authenticated users)
    this.renderNavbar(isAuthenticated);

    // Render the component
    this.renderComponent(route.component);
  }

  renderNavbar(isAuthenticated) {
    if (isAuthenticated) {
      new Navbar(this.navbarContainer);
    } else {
      this.navbarContainer.innerHTML = "";
    }
  }

  renderComponent(ComponentClass) {
    // Clear previous content
    this.contentContainer.innerHTML = "";

    // Render new component
    new ComponentClass(this.contentContainer);
  }

  // Static method for global navigation
  static navigate(path) {
    window.history.pushState({}, "", path);
    window.dispatchEvent(new CustomEvent("navigate", { detail: { path } }));
  }
}

// Global navigation function
window.navigateTo = (path) => {
  AppRouter.navigate(path);
};
