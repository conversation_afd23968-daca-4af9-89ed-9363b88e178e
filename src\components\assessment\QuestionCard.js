import { LikertScale } from './LikertScale.js';

export class QuestionCard {
  constructor(container, options = {}) {
    this.container = container;
    this.options = {
      questionNumber: options.questionNumber || 1,
      totalQuestions: options.totalQuestions || 1,
      questionText: options.questionText || '',
      questionId: options.questionId || 'question',
      value: options.value || null,
      onChange: options.onChange || (() => {}),
      prefix: options.prefix || '', // For BFI: "I see myself as someone who..."
      isReverse: options.isReverse || false,
      disabled: options.disabled || false,
      ...options
    };
    
    this.likertScale = null;
    this.render();
    this.initializeLikertScale();
  }

  render() {
    const { 
      questionNumber, 
      totalQuestions, 
      questionText, 
      prefix, 
      isReverse,
      disabled 
    } = this.options;

    this.container.innerHTML = `
      <div class="question-card bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <!-- Question header -->
        <div class="flex justify-between items-center mb-4">
          <div class="flex items-center space-x-3">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                ${questionNumber}
              </div>
            </div>
            <div class="text-sm text-gray-500">
              Question ${questionNumber} of ${totalQuestions}
            </div>
          </div>
          
          ${isReverse ? `
            <div class="flex items-center space-x-2">
              <div class="w-2 h-2 bg-orange-400 rounded-full"></div>
              <span class="text-xs text-orange-600 font-medium">Reverse Scored</span>
            </div>
          ` : ''}
        </div>

        <!-- Progress bar -->
        <div class="w-full bg-gray-200 rounded-full h-2 mb-6">
          <div 
            class="bg-blue-600 h-2 rounded-full transition-all duration-300" 
            style="width: ${(questionNumber / totalQuestions) * 100}%"
          ></div>
        </div>

        <!-- Question text -->
        <div class="mb-6">
          ${prefix ? `
            <div class="text-gray-600 text-sm mb-2 italic">
              ${prefix}
            </div>
          ` : ''}
          <h3 class="text-lg font-medium text-gray-900 leading-relaxed">
            ${questionText}
          </h3>
          ${isReverse ? `
            <div class="mt-2 text-sm text-orange-600 bg-orange-50 p-3 rounded-md">
              <div class="flex items-start space-x-2">
                <svg class="w-4 h-4 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
                <div>
                  <div class="font-medium">Reverse Scored Item</div>
                  <div class="text-xs mt-1">Your response to this question will be automatically reversed during scoring.</div>
                </div>
              </div>
            </div>
          ` : ''}
        </div>

        <!-- Likert scale container -->
        <div class="likert-container ${disabled ? 'opacity-50 pointer-events-none' : ''}">
          <!-- Likert scale will be rendered here -->
        </div>

        <!-- Help text -->
        <div class="mt-4 text-xs text-gray-500 bg-gray-50 p-3 rounded-md">
          <div class="flex items-start space-x-2">
            <svg class="w-4 h-4 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
            </svg>
            <div>
              <div class="font-medium">How to respond:</div>
              <div class="mt-1">Select the number that best represents how much you agree with the statement. Take your time and answer honestly.</div>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  initializeLikertScale() {
    const likertContainer = this.container.querySelector('.likert-container');
    
    this.likertScale = new LikertScale(likertContainer, {
      name: this.options.questionId,
      value: this.options.value,
      disabled: this.options.disabled,
      onChange: (value) => {
        this.options.onChange(value);
        this.updateVisualFeedback(value);
      }
    });

    // Set initial value if provided
    if (this.options.value) {
      this.likertScale.setValue(this.options.value);
      this.updateVisualFeedback(this.options.value);
    }
  }

  updateVisualFeedback(value) {
    // Add visual feedback to the question card when answered
    const card = this.container.querySelector('.question-card');
    if (value) {
      card.classList.add('border-green-200', 'bg-green-50');
      card.classList.remove('border-gray-200');
      
      // Add checkmark to question number
      const questionNumber = card.querySelector('.w-8.h-8.bg-blue-600');
      if (questionNumber && !questionNumber.querySelector('.checkmark')) {
        questionNumber.innerHTML = `
          <svg class="w-4 h-4 checkmark" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
          </svg>
        `;
        questionNumber.classList.remove('bg-blue-600');
        questionNumber.classList.add('bg-green-600');
      }
    } else {
      card.classList.remove('border-green-200', 'bg-green-50');
      card.classList.add('border-gray-200');
      
      // Reset question number
      const questionNumber = card.querySelector('.w-8.h-8.bg-green-600');
      if (questionNumber) {
        questionNumber.innerHTML = this.options.questionNumber;
        questionNumber.classList.remove('bg-green-600');
        questionNumber.classList.add('bg-blue-600');
      }
    }
  }

  getValue() {
    return this.likertScale ? this.likertScale.getValue() : null;
  }

  setValue(value) {
    if (this.likertScale) {
      this.likertScale.setValue(value);
      this.updateVisualFeedback(value);
    }
    this.options.value = value;
  }

  setDisabled(disabled) {
    if (this.likertScale) {
      this.likertScale.setDisabled(disabled);
    }
    this.options.disabled = disabled;
    
    const likertContainer = this.container.querySelector('.likert-container');
    if (disabled) {
      likertContainer.classList.add('opacity-50', 'pointer-events-none');
    } else {
      likertContainer.classList.remove('opacity-50', 'pointer-events-none');
    }
  }

  isValid() {
    return this.likertScale ? this.likertScale.isValid() : false;
  }

  showError(message) {
    if (this.likertScale) {
      this.likertScale.showError(message);
    }
  }

  clearError() {
    if (this.likertScale) {
      this.likertScale.clearError();
    }
  }

  scrollIntoView() {
    this.container.scrollIntoView({ 
      behavior: 'smooth', 
      block: 'center' 
    });
  }

  destroy() {
    if (this.likertScale) {
      this.likertScale.destroy();
    }
    this.container.innerHTML = '';
  }
}
