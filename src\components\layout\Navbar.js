import { AuthManager, Router } from "../../utils/auth.js";
import { AuthService } from "../../services/authService.js";

export class Navbar {
  constructor(container) {
    this.container = container;
    this.render();
    this.attachEventListeners();
  }

  render() {
    const user = AuthManager.getUser();
    const isAuthenticated = AuthManager.isAuthenticated();

    this.container.innerHTML = `
      <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
          <div class="flex justify-between h-16">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <h1 class="text-xl font-bold text-gray-800">PetaTalenta</h1>
              </div>
              ${
                isAuthenticated
                  ? `
                <div class="hidden md:block">
                  <div class="ml-10 flex items-baseline space-x-4">
                    <a href="#" id="nav-dashboard" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                      Dashboard
                    </a>
                    <a href="#" id="nav-assessment" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                      Assessment
                    </a>
                    <a href="#" id="nav-profile" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                      Profile
                    </a>
                  </div>
                </div>
              `
                  : ""
              }
            </div>

            <div class="flex items-center">
              ${
                isAuthenticated
                  ? `
                <div class="flex items-center space-x-4">
                  <span class="text-gray-700 text-sm">
                    Welcome, ${user?.email || "User"}
                  </span>
                  <button
                    id="logout-btn"
                    class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200"
                  >
                    Logout
                  </button>
                </div>
              `
                  : `
                <div class="flex items-center space-x-4">
                  <a href="#" id="nav-login" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                    Login
                  </a>
                  <a href="#" id="nav-register" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200">
                    Register
                  </a>
                </div>
              `
              }
            </div>
          </div>
        </div>
      </nav>
    `;
  }

  attachEventListeners() {
    // Navigation links
    const dashboardLink = this.container.querySelector("#nav-dashboard");
    const assessmentLink = this.container.querySelector("#nav-assessment");
    const profileLink = this.container.querySelector("#nav-profile");
    const loginLink = this.container.querySelector("#nav-login");
    const registerLink = this.container.querySelector("#nav-register");
    const logoutBtn = this.container.querySelector("#logout-btn");

    if (dashboardLink) {
      dashboardLink.addEventListener("click", (e) => {
        e.preventDefault();
        Router.navigate("/dashboard");
      });
    }

    if (assessmentLink) {
      assessmentLink.addEventListener("click", (e) => {
        e.preventDefault();
        Router.navigate("/assessment");
      });
    }

    if (profileLink) {
      profileLink.addEventListener("click", (e) => {
        e.preventDefault();
        Router.navigate("/profile");
      });
    }

    if (loginLink) {
      loginLink.addEventListener("click", (e) => {
        e.preventDefault();
        Router.navigate("/login");
      });
    }

    if (registerLink) {
      registerLink.addEventListener("click", (e) => {
        e.preventDefault();
        Router.navigate("/register");
      });
    }

    if (logoutBtn) {
      logoutBtn.addEventListener("click", async (e) => {
        e.preventDefault();
        await this.handleLogout();
      });
    }
  }

  async handleLogout() {
    try {
      await AuthService.logout();
      Router.navigate("/login");
    } catch (error) {
      console.error("Logout failed:", error);
      // Force logout even if API call fails
      AuthManager.logout();
      Router.navigate("/login");
    }
  }

  update() {
    this.render();
    this.attachEventListeners();
  }
}
